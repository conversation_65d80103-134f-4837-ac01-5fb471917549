#!/usr/bin/env python3
"""
Demo script to show HTTP server functionality
"""

import os
import time
import tempfile
from pathlib import Path
from src.agent_py.http_server import start_log_server, get_log_url, stop_log_server
from src.agent_py.task_manager import TaskManager


def main():
    """Demonstrate the HTTP server functionality"""
    print("=== HTTP Log Server Demo ===\n")
    
    # Create a temporary directory for demo
    temp_dir = tempfile.mkdtemp()
    logs_dir = os.path.join(temp_dir, "logs")
    
    # Set environment variables for demo
    os.environ["DATA_LOGS_DIR"] = logs_dir
    os.environ["LOG_SERVER_HOST"] = "127.0.0.1"
    os.environ["LOG_SERVER_PORT"] = "8080"
    
    try:
        print("1. Creating demo task manager and log files...")
        
        # Create task manager
        tm = TaskManager(logs_dir=logs_dir)
        
        # Create some demo log entries
        issue_key = "DEMO-123"
        comment_id = "456"
        
        tm.write_log(issue_key, comment_id, "Starting AI task execution")
        tm.write_log(issue_key, comment_id, "Cloning repository...")
        tm.write_log(issue_key, comment_id, "Running MonitoringAgent...")
        tm.write_log(issue_key, comment_id, "Committing changes...")
        tm.write_log(issue_key, comment_id, "Pushing changes to remote repository...")
        tm.write_log(issue_key, comment_id, "Cleaning up local repository...")
        tm.write_log(issue_key, comment_id, "Task completed successfully")
        
        print(f"   Created log file: {tm.get_log_file_path(issue_key, comment_id)}")
        
        print("\n2. Starting HTTP server...")
        
        # Start the HTTP server
        server_url = start_log_server()
        print(f"   Server started at: {server_url}")
        
        print("\n3. Getting log URL...")
        
        # Get the HTTP URL for the log file
        log_url = get_log_url(issue_key, comment_id)
        print(f"   Log URL: {log_url}")
        
        print("\n4. Simulating Jira comment with HTTP URL...")
        
        # Simulate what would be posted to Jira
        jira_comment = f"""
AI Task Completed Successfully

**Issue:** {issue_key}
**Branch:** 3.11.1_{issue_key}_{comment_id}
**Status:** success
**Message:** Task completed successfully
**Project:** demo/project

The task has been completed and changes have been committed to the branch `3.11.1_{issue_key}_{comment_id}`.

**Log File:** {log_url}
        """.strip()
        
        print(jira_comment)
        
        print(f"\n5. Server is running. You can access the log at:")
        print(f"   {log_url}")
        print("\n   Press Ctrl+C to stop the server...")
        
        # Keep server running for demo
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n6. Stopping server...")
            
    except Exception as e:
        print(f"Error: {e}")
        
    finally:
        # Clean up
        stop_log_server()
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        # Clean up environment variables
        os.environ.pop("DATA_LOGS_DIR", None)
        os.environ.pop("LOG_SERVER_HOST", None)
        os.environ.pop("LOG_SERVER_PORT", None)
        
        print("   Server stopped and cleanup completed.")


if __name__ == "__main__":
    main()
