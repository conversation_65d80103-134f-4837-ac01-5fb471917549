"""
Task management module for tracking processed comments and tasks
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class TaskManager:
    """Manages task storage and duplicate prevention"""
    
    def __init__(self, tasks_dir: str = None, logs_dir: str = None):
        self.tasks_dir = Path(tasks_dir or os.getenv("DATA_TASKS_DIR", "data/tasks"))
        self.logs_dir = Path(logs_dir or os.getenv("DATA_LOGS_DIR", "data/logs"))
        
        # Create directories if they don't exist
        self.tasks_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_comment_hash(self, issue_key: str, comment_id: str) -> str:
        """Generate a unique hash for a comment"""
        return hashlib.md5(f"{issue_key}_{comment_id}".encode()).hexdigest()
    
    def _get_task_file_path(self, issue_key: str, comment_id: str) -> Path:
        """Get the file path for storing task information"""
        comment_hash = self._get_comment_hash(issue_key, comment_id)
        return self.tasks_dir / f"task_{comment_hash}.json"
    
    def _get_log_file_path(self, issue_key: str, comment_id: str) -> Path:
        """Get the file path for storing task logs"""
        comment_hash = self._get_comment_hash(issue_key, comment_id)
        return self.logs_dir / f"task_{comment_hash}.log"
    
    def is_task_processed(self, issue_key: str, comment_id: str) -> bool:
        """Check if a task for this comment has already been processed"""
        task_file = self._get_task_file_path(issue_key, comment_id)
        return task_file.exists()

    def should_skip_task(self, issue_key: str, comment_id: str) -> bool:
        """Check if a task should be skipped based on configuration and task status"""
        task_info = self.get_task_info(issue_key, comment_id)
        if task_info is None:
            return False  # Task doesn't exist, don't skip

        # Check if task re-running is allowed
        allow_rerun = os.getenv("ALLOW_TASK_RERUN", "true").lower() == "true"

        status = task_info.get("status", "")

        if allow_rerun:
            # Only skip if the task completed successfully
            # Allow re-running failed, processing, or created tasks
            return status == "completed"
        else:
            # Skip any task that has been processed before
            return status in ["completed", "failed", "processing", "created"]
    
    def get_task_info(self, issue_key: str, comment_id: str) -> Optional[Dict[str, Any]]:
        """Get stored task information for a comment"""
        task_file = self._get_task_file_path(issue_key, comment_id)
        if not task_file.exists():
            return None
        
        try:
            with open(task_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error reading task file {task_file}: {e}")
            return None
    
    def save_task_info(self, task: Dict[str, Any], status: str = "created") -> None:
        """Save task information to storage"""
        issue_key = task.get("issue_key", "unknown")
        comment_id = task.get("comment_id", "unknown")
        
        task_file = self._get_task_file_path(issue_key, comment_id)
        
        task_data = {
            "task": task,
            "status": status,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "comment_hash": self._get_comment_hash(issue_key, comment_id)
        }
        
        try:
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"Error saving task file {task_file}: {e}")
    
    def update_task_status(self, issue_key: str, comment_id: str, status: str, 
                          result: Dict[str, Any] = None) -> None:
        """Update task status and result"""
        task_file = self._get_task_file_path(issue_key, comment_id)
        
        if not task_file.exists():
            print(f"Task file not found: {task_file}")
            return
        
        try:
            with open(task_file, 'r', encoding='utf-8') as f:
                task_data = json.load(f)
            
            task_data["status"] = status
            task_data["updated_at"] = datetime.now().isoformat()
            
            if result:
                task_data["result"] = result
            
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, indent=2, ensure_ascii=False)
                
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error updating task file {task_file}: {e}")
    
    def get_log_file_path(self, issue_key: str, comment_id: str) -> str:
        """Get the log file path for a task"""
        return str(self._get_log_file_path(issue_key, comment_id))
    
    def write_log(self, issue_key: str, comment_id: str, message: str) -> None:
        """Write a log message for a task"""
        log_file = self._get_log_file_path(issue_key, comment_id)
        timestamp = datetime.now().isoformat()

        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {message}\n")
        except IOError as e:
            print(f"Error writing to log file {log_file}: {e}")

    def clear_task_logs(self, issue_key: str, comment_id: str) -> None:
        """Clear existing logs for a task (useful when re-running)"""
        log_file = self._get_log_file_path(issue_key, comment_id)

        try:
            if log_file.exists():
                log_file.unlink()
        except IOError as e:
            print(f"Error clearing log file {log_file}: {e}")
    
    def get_task_logs(self, issue_key: str, comment_id: str) -> str:
        """Get all logs for a task"""
        log_file = self._get_log_file_path(issue_key, comment_id)
        
        if not log_file.exists():
            return ""
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                return f.read()
        except IOError as e:
            print(f"Error reading log file {log_file}: {e}")
            return ""
    
    def list_tasks(self, status: str = None) -> list[Dict[str, Any]]:
        """List all tasks, optionally filtered by status"""
        tasks = []
        
        for task_file in self.tasks_dir.glob("task_*.json"):
            try:
                with open(task_file, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)
                
                if status is None or task_data.get("status") == status:
                    tasks.append(task_data)
                    
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error reading task file {task_file}: {e}")
        
        return sorted(tasks, key=lambda x: x.get("created_at", ""))
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """Clean up task files older than specified days"""
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        cleaned_count = 0
        
        for task_file in self.tasks_dir.glob("task_*.json"):
            try:
                with open(task_file, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)
                
                created_at = datetime.fromisoformat(task_data.get("created_at", ""))
                if created_at < cutoff_date:
                    # Remove task file
                    task_file.unlink()
                    
                    # Remove corresponding log file
                    log_file = self.logs_dir / f"{task_file.stem}.log"
                    if log_file.exists():
                        log_file.unlink()
                    
                    cleaned_count += 1
                    
            except (json.JSONDecodeError, IOError, ValueError) as e:
                print(f"Error processing task file {task_file}: {e}")
        
        return cleaned_count
