#!/usr/bin/env python3
"""
Command-line interface for managing tasks
"""

import argparse
import json
from datetime import datetime
from .task_manager import Task<PERSON>anager


def list_tasks(args):
    """List all tasks or filter by status"""
    tm = TaskManager()
    tasks = tm.list_tasks(args.status)
    
    if not tasks:
        print("No tasks found.")
        return
    
    print(f"Found {len(tasks)} task(s):")
    print("-" * 80)
    
    for task_data in tasks:
        task = task_data["task"]
        print(f"Issue: {task['issue_key']}")
        print(f"Comment ID: {task['comment_id']}")
        print(f"Project: {task.get('project_name', 'N/A')}")
        print(f"Status: {task_data['status']}")
        print(f"Created: {task_data['created_at']}")
        print(f"Updated: {task_data['updated_at']}")
        print(f"Description: {task['task_description'][:100]}...")
        
        if "result" in task_data:
            result = task_data["result"]
            print(f"Result: {result.get('success', 'N/A')} - {result.get('message', 'N/A')}")
        
        print("-" * 80)


def show_task(args):
    """Show detailed information about a specific task"""
    tm = TaskManager()
    task_data = tm.get_task_info(args.issue_key, args.comment_id)
    
    if not task_data:
        print(f"Task not found for issue {args.issue_key}, comment {args.comment_id}")
        return
    
    print("Task Details:")
    print("=" * 50)
    print(json.dumps(task_data, indent=2, default=str))


def show_logs(args):
    """Show logs for a specific task"""
    tm = TaskManager()
    logs = tm.get_task_logs(args.issue_key, args.comment_id)
    
    if not logs:
        print(f"No logs found for issue {args.issue_key}, comment {args.comment_id}")
        return
    
    print(f"Logs for {args.issue_key} - {args.comment_id}:")
    print("=" * 50)
    print(logs)


def cleanup_tasks(args):
    """Clean up old tasks"""
    tm = TaskManager()
    cleaned_count = tm.cleanup_old_tasks(args.days)
    print(f"Cleaned up {cleaned_count} old task(s) (older than {args.days} days)")


def task_stats(args):
    """Show task statistics"""
    tm = TaskManager()
    
    all_tasks = tm.list_tasks()
    created_tasks = tm.list_tasks("created")
    processing_tasks = tm.list_tasks("processing")
    completed_tasks = tm.list_tasks("completed")
    failed_tasks = tm.list_tasks("failed")
    
    print("Task Statistics:")
    print("=" * 30)
    print(f"Total tasks: {len(all_tasks)}")
    print(f"Created: {len(created_tasks)}")
    print(f"Processing: {len(processing_tasks)}")
    print(f"Completed: {len(completed_tasks)}")
    print(f"Failed: {len(failed_tasks)}")
    
    if all_tasks:
        # Calculate success rate
        total_finished = len(completed_tasks) + len(failed_tasks)
        if total_finished > 0:
            success_rate = (len(completed_tasks) / total_finished) * 100
            print(f"Success rate: {success_rate:.1f}%")


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description="Task Management CLI")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List tasks command
    list_parser = subparsers.add_parser("list", help="List tasks")
    list_parser.add_argument("--status", help="Filter by status (created, processing, completed, failed)")
    list_parser.set_defaults(func=list_tasks)
    
    # Show task command
    show_parser = subparsers.add_parser("show", help="Show task details")
    show_parser.add_argument("issue_key", help="Issue key (e.g., PROJ-123)")
    show_parser.add_argument("comment_id", help="Comment ID")
    show_parser.set_defaults(func=show_task)
    
    # Show logs command
    logs_parser = subparsers.add_parser("logs", help="Show task logs")
    logs_parser.add_argument("issue_key", help="Issue key (e.g., PROJ-123)")
    logs_parser.add_argument("comment_id", help="Comment ID")
    logs_parser.set_defaults(func=show_logs)
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser("cleanup", help="Clean up old tasks")
    cleanup_parser.add_argument("--days", type=int, default=30, help="Delete tasks older than N days (default: 30)")
    cleanup_parser.set_defaults(func=cleanup_tasks)
    
    # Stats command
    stats_parser = subparsers.add_parser("stats", help="Show task statistics")
    stats_parser.set_defaults(func=task_stats)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    args.func(args)


if __name__ == "__main__":
    main()
