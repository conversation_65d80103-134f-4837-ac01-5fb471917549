import os
import time
import schedule
import dotenv
import asyncio

# Load environment variables from .env file
dotenv.load_dotenv()

from typing import List, Dict, Any
from .jira_client import Jira<PERSON><PERSON>
from .jira_parser import parse_comment, JiraComment, JiraIssue
from .agents.monitoring import MonitoringAgent
from minisweagent.models.litellm_model import LitellmModel
from minisweagent.environments.local import LocalEnvironment
from minisweagent.utils.log import logger
from .git_operations import setup_for_ai_task, clone_repository, commit_changes
from .task_manager import TaskManager
from .http_server import start_log_server, get_log_url


logger.info(f"JIRA_BASE_URL: {os.getenv('JIRA_BASE_URL')}")


async def fetch_and_process_comments():
    """
    Fetch comments from Jira and process AI tasks
    """
    logger.info("Checking for new Jira comments...")
    client = JiraClient()

    # Get JQL from environment variable with fallback
    jql = os.getenv("JIRA_JQL", "commentDate >= -1d")
    try:
        issues = await client.search({"jql": jql})
    except Exception as error:
        logger.error(f"Failed to fetch <PERSON>ra issues: {str(error)}")
        return

    for issue in issues.get("issues", []):
        issue_key = issue["key"]
        try:
            comments = await client.get_comments(issue_key)
        except Exception as error:
            logger.error(f"Failed to fetch comments for issue {issue_key}: {str(error)}")
            continue

        # Initialize task manager
        task_manager = TaskManager()

        for comment in comments:
            parsed = parse_comment(comment, issue)
            if parsed["is_ai_task"] and parsed["task_info"]:
                task = parsed["task_info"]
                issue_key = task["issue_key"]
                comment_id = task["comment_id"]

                # Check if this comment has already been processed
                if task_manager.is_task_processed(issue_key, comment_id):
                    logger.info(f"Task for comment {comment_id} in issue {issue_key} already processed, skipping")
                    continue

                logger.info(f"Found new AI task in issue {issue_key}: {task['task_description']}")

                # Save task info before processing
                task_manager.save_task_info(task, "created")

                # Process the task
                await assign_to_agent(task, task_manager)


async def assign_to_agent(task: Dict[str, Any], task_manager: TaskManager):
    """
    Assign the task to the appropriate agent
    """
    description = task["task_description"]
    project_name = task.get("project_name", "")
    version = task.get("version", "unknown")
    issue_key = task.get("issue_key", "unknown")
    comment_id = task.get("comment_id", "unknown")

    # Create a logger function that writes to both console and task log
    def log_message(message: str):
        logger.info(message)
        task_manager.write_log(issue_key, comment_id, message)

    if not project_name:
        error_msg = f"No project_name specified in task for issue {issue_key}"
        log_message(error_msg)
        task_manager.update_task_status(issue_key, comment_id, "failed",
                                      {"error": "No project_name specified"})
        return {"success": False, "error": "No project_name specified"}

    try:
        # Update task status to processing
        task_manager.update_task_status(issue_key, comment_id, "processing")

        # Step 1: Git clone project from ssh://******************:30004/gf/{project_name}
        repo_url = f"ssh://******************:30004/gf/{project_name}.git"

        # Create a workspace directory for this task
        workspace_dir = os.getenv("WORKSPACE_DIR", "./workspace")
        project_dir = os.path.join(workspace_dir, project_name.replace("/", "_"))

        log_message(f"Cloning repository {repo_url} to {project_dir}")
        await clone_repository(repo_url, project_dir, log_message)

        # Step 2: cd project, checkout branch to {version}_{issue_key}_{comment_id}
        task_branch = await setup_for_ai_task(
            version,
            issue_key,
            comment_id,
            project_dir,
            log_message,
        )
        log_message(f"Checked out branch: {task_branch}")

        # Step 3: Run MonitoringAgent in the project
        log_message(f"Running MonitoringAgent for task: {description}")

        # Initialize agent with project directory as working directory
        model_name = "openrouter/z-ai/glm-4.5-air:free"
        agent = MonitoringAgent(
            LitellmModel(model_name=model_name),
            LocalEnvironment(cwd=project_dir),
        )
        status, message = agent.run(description)
        log_message(f"Agent completed with status: {status}, message: {message}")

        # Step 4: Commit changes in the project
        commit_message = f"AI task completion for {issue_key}: {description[:50]}..."
        await commit_changes(project_dir, commit_message, log_message)
        log_message("Changes committed successfully")

        # Step 5: Post result in the comment of the original issue to Jira
        log_url = get_log_url(issue_key, comment_id)
        result_message = f"""
AI Task Completed Successfully

**Issue:** {issue_key}
**Branch:** {task_branch}
**Status:** {status}
**Message:** {message}
**Project:** {project_name}

The task has been completed and changes have been committed to the branch `{task_branch}`.

**Log File:** {log_url}
        """.strip()

        client = JiraClient()
        await client.add_comment(issue_key, result_message)
        log_message(f"Posted result comment to Jira issue {issue_key}")

        # Update task status to completed
        result_data = {
            "success": True,
            "branch": task_branch,
            "status": status,
            "message": message,
            "project_dir": project_dir,
        }
        task_manager.update_task_status(issue_key, comment_id, "completed", result_data)

        return result_data

    except Exception as error:
        error_msg = f"Error in assign_to_agent: {str(error)}"
        log_message(error_msg)

        # Update task status to failed
        task_manager.update_task_status(issue_key, comment_id, "failed",
                                      {"error": str(error)})

        # Post error to Jira
        try:
            log_url = get_log_url(issue_key, comment_id)
            error_message = f"""
AI Task Failed

**Issue:** {issue_key}
**Project:** {project_name}
**Error:** {str(error)}

The AI task could not be completed due to the above error.

**Log File:** {log_url}
            """.strip()

            client = JiraClient()
            await client.add_comment(issue_key, error_message)
            log_message(f"Posted error comment to Jira issue {issue_key}")
        except Exception as jira_error:
            log_message(f"Failed to post error to Jira: {str(jira_error)}")

        return {"success": False, "error": str(error)}


def main():
    """Main entry point for the Jira comment monitor"""
    # Start the HTTP server for serving log files
    try:
        server_url = start_log_server()
        logger.info(f"Log server started at {server_url}")
    except Exception as e:
        logger.error(f"Failed to start log server: {e}")
        return 1

    # Get sync interval from environment variable with fallback (5 minutes)
    sync_interval = int(os.getenv("JIRA_SYNC_INTERVAL_MINUTES", "5"))
    schedule.every(sync_interval).minutes.do(lambda: asyncio.run(fetch_and_process_comments()))

    logger.info(
        f"Jira comment monitor started (sync interval: {sync_interval} minutes). Press Ctrl+C to exit."
    )

    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping Jira comment monitor...")

    return 0


if __name__ == "__main__":
    main()
