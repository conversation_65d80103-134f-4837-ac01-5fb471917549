import subprocess
import os
import shutil
from typing import Optional, Callable


def log(message: str, log_callback: Optional[Callable[[str], None]] = None) -> None:
    """Log a message using the provided callback."""
    if log_callback:
        log_callback(message)


async def execute_git_command(
    command: str,
    args: list[str],
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Execute a Git command and return the output."""
    log(f"Executing: git {command} {' '.join(args)}", log_callback)

    try:
        result = subprocess.run(
            ["git", command] + args,
            cwd=repo_path,
            capture_output=True,
            text=True,
            check=True,
        )
        if result.stderr:
            log(f"stderr: {result.stderr}", log_callback)
        log(f"stdout: {result.stdout}", log_callback)
        return result.stdout
    except subprocess.CalledProcessError as error:
        log(f"Error: {error.stderr}", log_callback)
        raise error


async def clone_repository(
    repo_url: str,
    target_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Clone a Git repository to the target path."""
    log(f"Cloning repository {repo_url} to {target_path}", log_callback)

    # Remove target directory if it exists
    if os.path.exists(target_path):
        log(f"Removing existing directory: {target_path}", log_callback)
        shutil.rmtree(target_path)

    # Create parent directory if it doesn't exist
    parent_dir = os.path.dirname(target_path)
    if parent_dir and not os.path.exists(parent_dir):
        os.makedirs(parent_dir, exist_ok=True)

    try:
        result = subprocess.run(
            ["git", "clone", repo_url, target_path],
            capture_output=True,
            text=True,
            check=True,
        )
        if result.stderr:
            log(f"stderr: {result.stderr}", log_callback)
        log(f"Successfully cloned repository to {target_path}", log_callback)
        return target_path
    except subprocess.CalledProcessError as error:
        log(f"Error cloning repository: {error.stderr}", log_callback)
        raise error


async def checkout_branch(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Checkout a Git branch. Create it if it doesn't exist."""
    try:
        await execute_git_command("checkout", [branch_name], repo_path, log_callback)
        log(f"Successfully checked out branch: {branch_name}", log_callback)
    except subprocess.CalledProcessError:
        log(f"Branch {branch_name} does not exist. Will create it.", log_callback)
        await create_branch(branch_name, repo_path, log_callback)


async def create_branch(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Create and checkout a new Git branch."""
    await execute_git_command("checkout", ["-b", branch_name], repo_path, log_callback)
    log(f"Successfully created and checked out branch: {branch_name}", log_callback)


async def has_remote(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> bool:
    """Check if the repository has a remote."""
    try:
        output = await execute_git_command("remote", [], repo_path, log_callback)
        return output.strip() != ""
    except subprocess.CalledProcessError:
        log("Error checking for remote, assuming no remote exists", log_callback)
        return False


async def pull_rebase(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Pull changes with rebase."""
    if await has_remote(repo_path, log_callback):
        try:
            await execute_git_command("pull", ["-r"], repo_path, log_callback)
            log("Successfully pulled with rebase", log_callback)
        except subprocess.CalledProcessError as error:
            log(
                f"Error during pull: {error.stderr}. Continuing without pull.",
                log_callback,
            )
    else:
        log("Repository has no remote, skipping git pull", log_callback)


async def get_current_branch(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Get the current Git branch name."""
    output = await execute_git_command(
        "branch", ["--show-current"], repo_path, log_callback
    )
    return output.strip()


async def branch_exists(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> bool:
    """Check if a Git branch exists."""
    try:
        output = await execute_git_command(
            "branch", ["--list", branch_name], repo_path, log_callback
        )
        return output.strip() != ""
    except subprocess.CalledProcessError:
        return False


async def commit_changes(
    repo_path: str,
    message: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Add all changes and commit with the given message."""
    try:
        # Add all changes
        await execute_git_command("add", ["."], repo_path, log_callback)
        log("Added all changes to staging", log_callback)

        # Commit changes
        await execute_git_command("commit", ["-m", message], repo_path, log_callback)
        log(f"Successfully committed changes with message: {message}", log_callback)
    except subprocess.CalledProcessError as error:
        log(f"Error committing changes: {error.stderr}", log_callback)
        raise error


async def setup_for_ai_task(
    fix_version: str,
    issue_key: str,
    comment_id: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Setup Git branches for an AI task."""
    # Step 1: Checkout the base branch
    base_branch = f"{fix_version}_{issue_key}_{comment_id}"
    await checkout_branch(base_branch, repo_path, log_callback)

    return base_branch
