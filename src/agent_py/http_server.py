"""
HTTP server for serving log files
"""

import os
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
from pathlib import Path
from typing import Optional
from minisweagent.utils.log import logger


class LogFileHandler(SimpleHTTPRequestHandler):
    """Custom handler for serving log files"""
    
    def __init__(self, *args, logs_dir: str = None, **kwargs):
        self.logs_dir = Path(logs_dir) if logs_dir else Path("data/logs")
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests for log files"""
        # Remove leading slash and decode URL
        path = self.path.lstrip('/')
        
        # Security check: only allow access to .log files
        if not path.endswith('.log'):
            self.send_error(404, "File not found")
            return
        
        # Construct full file path
        file_path = self.logs_dir / path
        
        # Security check: ensure the file is within logs directory
        try:
            file_path.resolve().relative_to(self.logs_dir.resolve())
        except ValueError:
            self.send_error(403, "Access denied")
            return
        
        # Check if file exists
        if not file_path.exists():
            self.send_error(404, "Log file not found")
            return
        
        # Serve the file
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error serving log file {file_path}: {e}")
            self.send_error(500, "Internal server error")
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr"""
        logger.debug(f"HTTP: {format % args}")


class LogServer:
    """HTTP server for serving log files"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8080, logs_dir: str = None):
        self.host = host
        self.port = port
        self.logs_dir = logs_dir or os.getenv("DATA_LOGS_DIR", "data/logs")
        self.server: Optional[HTTPServer] = None
        self.thread: Optional[threading.Thread] = None
        self._running = False
    
    def start(self) -> str:
        """Start the HTTP server in a background thread"""
        if self._running:
            logger.warning("Log server is already running")
            return f"http://{self.host}:{self.port}"
        
        # Create logs directory if it doesn't exist
        Path(self.logs_dir).mkdir(parents=True, exist_ok=True)
        
        # Create handler class with logs_dir
        def handler_factory(*args, **kwargs):
            return LogFileHandler(*args, logs_dir=self.logs_dir, **kwargs)
        
        try:
            self.server = HTTPServer((self.host, self.port), handler_factory)
            self._running = True
            
            # Start server in background thread
            self.thread = threading.Thread(target=self._run_server, daemon=True)
            self.thread.start()
            
            # Wait a moment to ensure server starts
            time.sleep(0.1)
            
            base_url = f"http://{self.host}:{self.port}"
            logger.info(f"Log server started at {base_url}")
            return base_url
            
        except Exception as e:
            logger.error(f"Failed to start log server: {e}")
            self._running = False
            raise
    
    def _run_server(self):
        """Run the HTTP server"""
        try:
            logger.info(f"Starting HTTP server on {self.host}:{self.port}")
            self.server.serve_forever()
        except Exception as e:
            logger.error(f"HTTP server error: {e}")
        finally:
            self._running = False
    
    def stop(self):
        """Stop the HTTP server"""
        if self.server and self._running:
            logger.info("Stopping log server...")
            self.server.shutdown()
            self.server.server_close()
            self._running = False
            
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=5)
    
    def get_log_url(self, issue_key: str, comment_id: str) -> str:
        """Get the HTTP URL for a specific log file"""
        from .task_manager import TaskManager
        
        # Get the log file name from task manager
        tm = TaskManager(logs_dir=self.logs_dir)
        log_file_path = Path(tm.get_log_file_path(issue_key, comment_id))
        log_filename = log_file_path.name
        
        base_url = f"http://{self.host}:{self.port}"
        return f"{base_url}/{log_filename}"
    
    def is_running(self) -> bool:
        """Check if the server is running"""
        return self._running


# Global server instance
_log_server: Optional[LogServer] = None


def get_log_server() -> LogServer:
    """Get or create the global log server instance"""
    global _log_server
    
    if _log_server is None:
        # Get configuration from environment
        host = os.getenv("LOG_SERVER_HOST", "0.0.0.0")
        port = int(os.getenv("LOG_SERVER_PORT", "8080"))
        logs_dir = os.getenv("DATA_LOGS_DIR", "data/logs")
        
        _log_server = LogServer(host=host, port=port, logs_dir=logs_dir)
    
    return _log_server


def start_log_server() -> str:
    """Start the global log server and return its base URL"""
    server = get_log_server()
    return server.start()


def stop_log_server():
    """Stop the global log server"""
    global _log_server
    if _log_server:
        _log_server.stop()
        _log_server = None


def get_log_url(issue_key: str, comment_id: str) -> str:
    """Get the HTTP URL for a specific log file"""
    server = get_log_server()
    if not server.is_running():
        start_log_server()
    return server.get_log_url(issue_key, comment_id)
