"""
MonitoringAgent class to track agent and LLM interactions.
"""

import logging
from typing import Dict, Any

from minisweagent.agents.default import DefaultAgent
from minisweagent.utils.log import logger


class MonitoringAgent(DefaultAgent):
    """
    A subclass of DefaultAgent that monitors interactions between the agent and LLM.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger.info("MonitoringAgent initialized")

    def query(self) -> Dict[str, Any]:
        """
        Query the model and log the interaction.
        """
        logger.debug(f"Querying LLM with messages: {self.messages}")
        response = super().query()
        logger.debug(f"LLM response: {response}")
        return response

    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the action and log the result.
        """
        logger.debug(f"Executing action: {action['action']}")
        try:
            output = super().execute_action(action)
            logger.debug(f"Action output: {output}")
            return output
        except Exception as e:
            logger.error(f"Action execution failed: {e}")
            raise

    def run(self, task: str, **kwargs) -> tuple[str, str]:
        """
        Run the agent and log the final status.
        """
        logger.info(f"Starting task: {task}")
        try:
            status, message = super().run(task, **kwargs)
            logger.info(f"Task completed with status: {status}")
            return status, message
        except Exception as e:
            logger.error(f"Task failed: {e}")
            raise