from typing import Dict, Any, Optional, TypedDict

# Constants
AI_TASK_MARKER = "```ai-task"
CODE_BLOCK_END = "```"


class JiraComment(TypedDict):
    """Type definition for Jira comment"""
    body: str


class JiraIssue(TypedDict):
    """Type definition for Jira issue"""
    key: str
    fields: Dict[str, Any]


class AITaskComment(TypedDict):
    """Type definition for parsed AI task"""
    issue_key: str
    version: str
    task_description: str
    branch: str
    project_name: str
    comment_id: str


class ParsedAIComment(TypedDict):
    """Type definition for parsed comment"""
    is_ai_task: bool
    task_info: Optional[AITaskComment]
    original_comment: <PERSON><PERSON><PERSON>om<PERSON>


def parse_comment(comment: <PERSON>raComment, issue: <PERSON>raIssue) -> ParsedAIComment:
    """
    Parse a Jira comment to check if it contains an AI task
    """
    if AI_TASK_MARKER not in comment["body"]:
        return {
            "is_ai_task": False,
            "task_info": None,
            "original_comment": comment,
        }

    try:
        task_block = extract_task_block(comment["body"])
        if not task_block:
            return {
                "is_ai_task": False,
                "task_info": None,
                "original_comment": comment,
            }

        # Extract comment ID from comment
        comment_id = comment.get("id", "unknown")
        task_info = parse_task_block(task_block, issue, comment_id)

        return {
            "is_ai_task": True,
            "task_info": task_info,
            "original_comment": comment,
        }
    except Exception as error:
        # Import logger here to avoid circular imports
        from minisweagent.utils.log import logger
        logger.error(f"Error parsing AI task comment: {error}")
        return {
            "is_ai_task": False,
            "task_info": None,
            "original_comment": comment,
        }


def extract_task_block(comment_body: str) -> Optional[str]:
    """
    Extract the AI task block from a comment body
    """
    start_index = comment_body.find(AI_TASK_MARKER)
    if start_index == -1:
        return None

    end_index = comment_body.find(
        CODE_BLOCK_END, start_index + len(AI_TASK_MARKER)
    )
    if end_index == -1:
        return None

    return comment_body[start_index + len(AI_TASK_MARKER) : end_index].strip()


def parse_task_block(block: str, issue: JiraIssue, comment_id: str) -> AITaskComment:
    """
    Parse the task block to extract description, project_name, etc.
    """
    lines = block.split("\n")
    description: list[str] = []
    project_name = ""
    is_description = False

    # Get version from fixVersions, default to the first one
    fix_versions = issue["fields"].get("fixVersions", [])
    version = fix_versions[0].get("name", "3.11.1") if fix_versions else "3.11.1"

    for line in lines:
        trimmed_line = line.strip()

        if trimmed_line.startswith("project:"):
            project_name = trimmed_line[len("project:") :].strip()
        elif trimmed_line.startswith("description:"):
            is_description = True
            if len(trimmed_line) > len("description:"):
                description.append(
                    trimmed_line[len("description:") :].strip()
                )
        elif is_description and trimmed_line:
            # Skip lines that start with "tool:" since we don't use it anymore
            if not trimmed_line.startswith("tool:"):
                description.append(trimmed_line)

    branch = f"{version}_{issue['key']}_{comment_id}"

    return {
        "issue_key": issue["key"],
        "version": version,
        "task_description": "\n".join(description).strip(),
        "branch": branch,
        "project_name": project_name,
        "comment_id": comment_id,
    }