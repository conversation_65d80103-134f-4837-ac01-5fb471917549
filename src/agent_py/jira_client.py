import os
import base64
import aiohttp
from typing import Dict, Any, Optional, List, TypedDict

class JiraConfig(TypedDict):
    base_url: str
    api_token: str
    home: str
    username: str
    password: str
    authorization: str
    jsessionid: str
    cookie: str

class JiraClient:
    """
    Client for interacting with the Jira API
    """

    def __init__(self):
        self.config: JiraConfig = {
            "base_url": os.getenv("JIRA_BASE_URL", ""),
            "api_token": os.getenv("JIRA_API_TOKEN", ""),
            "home": os.getenv("JIRA_HOME", ""),
            "username": os.getenv("JIRA_USERNAME", ""),
            "password": os.getenv("JIRA_PASSWORD", ""),
            "authorization": os.getenv("JIRA_AUTHORIZATION", ""),
            "jsessionid": os.getenv("JIRA_JSESSIONID", ""),
            "cookie": os.getenv("JIRA_COOKIE", ""),
        }

        # Validate required configuration
        if not self.config["base_url"]:
            raise JiraError("JIRA_BASE_URL environment variable is required")

    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get Jira authentication headers from config
        """
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }

        # Determine authentication method based on available config
        if self.config["authorization"]:
            # Basic auth with authorization token
            headers["Authorization"] = f"Basic {self.config['authorization']}"
        elif self.config["username"] and self.config["password"]:
            # Basic auth with username/password
            credentials = f"{self.config['username']}:{self.config['password']}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
            headers["Authorization"] = f"Basic {encoded_credentials}"
        elif self.config["cookie"]:
            # Cookie-based auth with custom cookie
            headers["Cookie"] = self.config["cookie"]
        elif self.config["jsessionid"]:
            # Cookie-based auth with JSESSIONID
            headers["Cookie"] = f"JSESSIONID={self.config['jsessionid']}"
        else:
            # Fallback to API token
            headers["Authorization"] = f"Bearer {self.config['api_token']}"

        # Only add Host, Origin, Referer if home is configured and not empty
        if self.config.get("home") and self.config["home"].strip():
            try:
                from urllib.parse import urlparse
                parsed = urlparse(self.config["home"])
                if parsed.netloc:
                    headers["Host"] = parsed.netloc
                    headers["Origin"] = self.config["home"]
                    headers["Referer"] = self.config["home"]
            except Exception:
                # If URL parsing fails, skip these headers
                pass

        return headers

    async def request(self, method: str, path: str, data: Optional[Dict[str, Any]] = None) -> Any:
        """
        Make an async request to the Jira API
        """
        # Ensure proper URL construction
        base_url = self.config['base_url'].rstrip('/') if self.config['base_url'] else ''
        path = path.lstrip('/')
        url = f"{base_url}/{path}" if base_url else path
        headers = self._get_auth_headers()

        async with aiohttp.ClientSession() as session:
            # For GET requests, use query parameters; for POST/PUT, use JSON body
            if method.upper() == "GET" and data:
                async with session.request(
                    method,
                    url,
                    headers=headers,
                    params=data,
                ) as response:
                    if not response.ok:
                        try:
                            error_data = await response.json()
                            error_messages = error_data.get("errorMessages", ["An error occurred"])
                            raise JiraError(f"HTTP {response.status}: {', '.join(error_messages)}")
                        except ValueError:
                            # Handle invalid JSON response (e.g., HTML error page)
                            response_text = await response.text()
                            raise JiraError(f"HTTP {response.status}: Invalid response from Jira API (URL: {url}): {response_text[:500]}...")

                    try:
                        return await response.json()
                    except ValueError:
                        response_text = await response.text()
                        raise JiraError(f"Failed to parse JSON response from {url}: {response_text[:500]}...")
            else:
                async with session.request(
                    method,
                    url,
                    headers=headers,
                    json=data,
                ) as response:
                    if not response.ok:
                        try:
                            error_data = await response.json()
                            error_messages = error_data.get("errorMessages", ["An error occurred"])
                            raise JiraError(f"HTTP {response.status}: {', '.join(error_messages)}")
                        except ValueError:
                            # Handle invalid JSON response (e.g., HTML error page)
                            response_text = await response.text()
                            raise JiraError(f"HTTP {response.status}: Invalid response from Jira API (URL: {url}): {response_text[:500]}...")

                    try:
                        return await response.json()
                    except ValueError:
                        response_text = await response.text()
                        raise JiraError(f"Failed to parse JSON response from {url}: {response_text[:500]}...")

    async def search(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Search for Jira issues
        """
        # Try GET method first (many JIRA instances prefer GET for search)
        try:
            return await self.request("GET", "/rest/api/2/search", parameters)
        except JiraError as e:
            # If GET fails, try POST as fallback
            if "405" in str(e) or "Method Not Allowed" in str(e):
                return await self.request("POST", "/rest/api/2/search", parameters)
            else:
                raise

    async def get_issue(self, issue_key: str) -> Dict[str, Any]:
        """
        Get a Jira issue by key
        """
        return await self.request("GET", f"/rest/api/2/issue/{issue_key}")

    async def get_comments(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        Get comments for a Jira issue
        """
        response = await self.request("GET", f"/rest/api/2/issue/{issue_key}/comment")
        return response.get("comments", [])

    async def add_comment(self, issue_key: str, body: str) -> Dict[str, Any]:
        """
        Add a comment to a Jira issue
        """
        return await self.request("POST", f"/rest/api/2/issue/{issue_key}/comment", {"body": body})


class JiraError(Exception):
    """Custom exception for Jira API errors"""
    pass