.PHONY: test test-verbose test-coverage install install-dev clean lint format run-monitor run-tasks help

# Default target
help:
	@echo "Available targets:"
	@echo "  test          - Run all tests"
	@echo "  test-verbose  - Run tests with verbose output"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  install       - Install dependencies"
	@echo "  install-dev   - Install development dependencies"
	@echo "  run-monitor   - Start the Jira comment monitor"
	@echo "  run-tasks     - Run task management CLI (use ARGS='command' for arguments)"
	@echo "  clean         - Clean up cache files"
	@echo "  lint          - Run linting (if configured)"
	@echo "  format        - Format code (if configured)"

# Install dependencies
install:
	uv sync

# Install development dependencies including test tools
install-dev:
	uv sync --extra test

# Run tests
test:
	pytest tests/

# Run tests with verbose output
test-verbose:
	pytest tests/ -v

# Run tests with coverage (requires pytest-cov)
test-coverage:
	pytest tests/ --cov=src --cov-report=html --cov-report=term

# Run the Jira comment monitor
run-monitor:
	python monitor.py

# Run task management CLI (pass arguments like: make run-tasks ARGS="stats")
run-tasks:
	python tasks.py $(ARGS)

# Clean up cache files
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	rm -rf .pytest_cache/ 2>/dev/null || true
	rm -rf htmlcov/ 2>/dev/null || true
	rm -rf .coverage 2>/dev/null || true

# Placeholder for linting (can be extended with ruff, flake8, etc.)
lint:
	@echo "Linting not configured yet"

# Placeholder for formatting (can be extended with black, ruff format, etc.)
format:
	@echo "Formatting not configured yet"
