# Agent-Py: Jira-Integrated AI Task Automation

A Python-based system that monitors Jira comments for AI tasks and automatically executes them using the MonitoringAgent. The system can clone repositories, create branches, run AI agents, commit changes, and post results back to <PERSON>ra.

## Features

- **Jira Integration**: Monitors Jira comments for AI task markers
- **Multi-Repository Support**: Can clone and work with different Git repositories
- **Automated Workflow**: Complete workflow from task detection to result posting
- **Branch Management**: Automatic branch creation and management
- **AI Agent Execution**: Uses MonitoringAgent for task execution
- **Result Reporting**: Posts execution results back to <PERSON>ra comments
- **Task Management**: Prevents duplicate processing of the same comment
- **Comprehensive Logging**: All agent activities logged to dedicated files
- **HTTP Log Server**: Built-in HTTP server for serving log files via URLs
- **Task Persistence**: Task information stored for tracking and auditing

## Workflow

For each AI task found in Jira comments, the system:

1. **Git Clone**: Clones the specified project from `ssh://******************:30004/gf/{project_name}.git`
2. **Branch Checkout**: Creates and checks out branch `{version}_{issue_key}_{comment_id}`
3. **Agent Execution**: Runs MonitoringAgent in the project directory
4. **Commit Changes**: Commits all changes with a descriptive message
5. **Result Posting**: Posts execution results back to the original Jira issue

## AI Task Format

To trigger an AI task, add a comment to a Jira issue with the following format:

```
```ai-task
project: gifts/pb_package
description: Fix the bug in the payment processing module
Add proper error handling for failed transactions
Ensure all edge cases are covered
```
```

### Required Fields

- **project**: The project name (e.g., `gifts/pb_package`) - used to construct the Git URL
- **description**: Multi-line description of the task to be performed

All tasks are processed using the MonitoringAgent.

## Configuration

Copy `.env.example` to `.env` and configure the following variables:

### Jira Configuration

```bash
# Basic authentication
JIRA_BASE_URL=https://your-jira-instance.com
JIRA_USERNAME=your-username
JIRA_PASSWORD=your-password

# Alternative: Authorization token
JIRA_AUTHORIZATION=your-base64-encoded-credentials

# Alternative: API token
JIRA_API_TOKEN=your-api-token

# Alternative: Cookie-based auth
JIRA_COOKIE=JSESSIONID=your-session-id
JIRA_HOME=https://your-jira-instance.com
```

### Query Configuration

```bash
# JQL query to find issues with recent comments
JIRA_JQL=commentDate >= -1d

# Sync interval in minutes
JIRA_SYNC_INTERVAL_MINUTES=5
```

### Workspace Configuration

```bash
# Directory where repositories will be cloned
WORKSPACE_DIR=./workspace

# Task and log storage directories
DATA_TASKS_DIR=data/tasks
DATA_LOGS_DIR=data/logs
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd agent-py
   ```

2. **Install dependencies**:
   ```bash
   # Install main dependencies
   uv sync

   # Install with test dependencies
   uv sync --extra test
   ```

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your Jira credentials and configuration
   ```

## Usage

### Running the Application

Start the Jira comment monitor:

```bash
# Using the main entry point
python main.py monitor

# Using the simple entry point
python monitor.py

# Using the Makefile
make run-monitor
```

The monitor will:
- Check for new Jira comments every 5 minutes (configurable)
- Process any AI tasks found in comments
- Log all activities to the console

### Running Tests

The project uses pytest for testing. Run tests with:

```bash
# Run all tests
pytest tests/

# Run tests with verbose output
pytest tests/ -v

# Run specific test file
pytest tests/test_parser.py -v

# Or use the Makefile
make test
make test-verbose
```

### Managing Tasks

Use the task CLI to manage and monitor tasks:

```bash
# Using the main entry point
python main.py tasks list
python main.py tasks stats
python main.py tasks show PROJ-123 12345

# Using the simple entry point
python tasks.py list
python tasks.py stats

# Using the Makefile
make run-tasks
```

Or use the task CLI directly:

```bash
# List all tasks
python tasks.py list

# List tasks by status
python tasks.py list --status completed

# Show detailed task information
python tasks.py show PROJ-123 12345

# View task logs
python tasks.py logs PROJ-123 12345

# Show task statistics
python tasks.py stats

# Clean up old tasks (older than 30 days)
python tasks.py cleanup --days 30
```

## Project Structure

```
agent-py/
├── main.py              # Main entry point with subcommands
├── monitor.py           # Simple monitor entry point
├── tasks.py             # Simple task CLI entry point
├── src/
│   └── agent_py/        # Main package
│       ├── __init__.py
│       ├── monitor.py       # Jira comment monitoring
│       ├── jira_client.py   # Jira API client
│       ├── jira_parser.py   # Comment parsing logic
│       ├── git_operations.py # Git operations (clone, branch, commit)
│       ├── task_manager.py  # Task storage and duplicate prevention
│       ├── task_cli.py      # Command-line interface for task management
│       └── agents/
│           └── monitoring.py # MonitoringAgent implementation
├── tests/                # Test suite (pytest-based)
│   ├── test_parser.py        # Parser tests
│   ├── test_task_manager.py  # TaskManager tests
│   ├── test_git_operations.py # Git operations tests
│   ├── test_local_environment.py # LocalEnvironment tests
│   ├── test_workflow.py      # Full workflow tests
│   └── test_backward_compatibility.py # Backward compatibility tests
├── data/
│   ├── tasks/           # Task information storage
│   └── logs/            # Agent execution logs
├── .env.example          # Environment configuration template
├── pytest.ini           # Pytest configuration
├── Makefile             # Build and test automation
└── README.md            # This file
```

## Example Workflow

1. **Create Jira Comment**:
   ```
   ```ai-task
   project: gifts/pb_package
   description: Add unit tests for the payment validation module
   Ensure all edge cases are covered
   ```
   ```

2. **System Processing**:
   - Detects the AI task in the comment
   - Clones `ssh://******************:30004/gf/gifts/pb_package.git`
   - Creates branch `3.11.1_PROJ-123_12345`
   - Runs MonitoringAgent with the task description
   - Commits any changes made by the agent
   - Posts results back to the Jira issue

3. **Result Comment**:
   ```
   AI Task Completed Successfully

   **Issue:** PROJ-123
   **Branch:** 3.11.1_PROJ-123_12345
   **Status:** success
   **Message:** Task completed successfully
   **Project:** gifts/pb_package

   The task has been completed and changes have been committed to the branch `3.11.1_PROJ-123_12345`.

   **Log File:** http://0.0.0.0:8080/task_abc123def456.log
   ```

## Task Management Features

### Duplicate Prevention

The system automatically prevents duplicate processing of the same comment:

- Each comment is uniquely identified by `issue_key` + `comment_id`
- Task information is stored in `DATA_TASKS_DIR` before processing
- If a comment has already been processed, it will be skipped
- Task status is tracked throughout the lifecycle: `created` → `processing` → `completed`/`failed`

### Comprehensive Logging

All agent activities are logged to dedicated files:

- Logs are stored in `DATA_LOGS_DIR` with unique filenames per task
- Both console output and task-specific logs are maintained
- Log files are served via HTTP server for easy web access
- HTTP URLs are included in Jira result comments for direct access
- Logs include timestamps and detailed execution information

### HTTP Log Server

The system includes a built-in HTTP server for serving log files:

- Automatically starts when the monitor begins
- Serves log files at `http://{HOST}:{PORT}/{log_filename}.log`
- Default configuration: `0.0.0.0:8080`
- Secure access: only `.log` files within the logs directory are served
- Log URLs are posted in Jira comments for easy access

#### Configuration

Configure the HTTP server using environment variables:

```bash
# HTTP Server Configuration
LOG_SERVER_HOST=0.0.0.0    # Server host (default: 0.0.0.0)
LOG_SERVER_PORT=8080       # Server port (default: 8080)
```

#### AI Model Configuration

Configure the AI model used by the MonitoringAgent:

```bash
# AI Model Configuration
AI_MODEL_NAME=openrouter/z-ai/glm-4.5-air:free  # AI model name (default: openrouter/z-ai/glm-4.5-air:free)
```

You can use any model supported by LiteLLM, such as:
- `openrouter/z-ai/glm-4.5-air:free` (default, free model)
- `gpt-4o-mini`
- `claude-3-haiku-20240307`
- `gemini/gemini-1.5-flash`

### Task Persistence

Task information is stored for tracking and auditing:

- Task metadata stored as JSON files in `DATA_TASKS_DIR`
- Includes task details, status, timestamps, and results
- Enables task listing, filtering, and statistics
- Supports cleanup of old tasks to manage storage

## Error Handling

The system includes comprehensive error handling:

- **Git Errors**: Repository cloning and branch operations
- **Agent Errors**: AI agent execution failures
- **Jira Errors**: API communication issues
- **Parsing Errors**: Invalid comment format handling

All errors are logged and posted back to the original Jira issue as comments.

## Dependencies

- **Python 3.13+**
- **aiohttp**: Async HTTP client for Jira API
- **mini-swe-agent**: AI agent framework
- **python-dotenv**: Environment variable management
- **schedule**: Task scheduling

## Testing

The project uses pytest for comprehensive testing:

### Test Coverage

- ✅ **Parser tests**: Comment parsing and field extraction
- ✅ **Task manager tests**: Duplicate prevention and storage
- ✅ **Git operations tests**: Repository cloning and branching
- ✅ **LocalEnvironment tests**: Working directory handling
- ✅ **Full workflow tests**: End-to-end simulation
- ✅ **Backward compatibility tests**: Legacy comment support

### Running Tests

```bash
# Install test dependencies
uv sync --extra test

# Run all tests
pytest tests/

# Run with verbose output
pytest tests/ -v

# Run specific test file
pytest tests/test_parser.py

# Using Makefile
make test
make test-verbose
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]