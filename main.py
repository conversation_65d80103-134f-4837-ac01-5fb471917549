#!/usr/bin/env python3
"""
Agent-Py: Jira-Integrated AI Task Automation

Main entry point for the application.
"""

import sys
import argparse
from src.agent_py.monitor import main as monitor_main
from src.agent_py.task_cli import main as task_cli_main


def main():
    """Main entry point with subcommands"""
    parser = argparse.ArgumentParser(
        description="Agent-Py: Jira-Integrated AI Task Automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s monitor              # Start the Jira comment monitor
  %(prog)s tasks list           # List all tasks
  %(prog)s tasks stats          # Show task statistics
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Monitor subcommand
    monitor_parser = subparsers.add_parser(
        "monitor", 
        help="Start the Jira comment monitor"
    )
    
    # Tasks subcommand
    tasks_parser = subparsers.add_parser(
        "tasks", 
        help="Manage tasks"
    )
    
    # Add task management arguments
    tasks_subparsers = tasks_parser.add_subparsers(dest="task_command", help="Task commands")
    
    # List tasks
    list_parser = tasks_subparsers.add_parser("list", help="List tasks")
    list_parser.add_argument("--status", help="Filter by status")
    
    # Show task
    show_parser = tasks_subparsers.add_parser("show", help="Show task details")
    show_parser.add_argument("issue_key", help="Issue key")
    show_parser.add_argument("comment_id", help="Comment ID")
    
    # Show logs
    logs_parser = tasks_subparsers.add_parser("logs", help="Show task logs")
    logs_parser.add_argument("issue_key", help="Issue key")
    logs_parser.add_argument("comment_id", help="Comment ID")
    
    # Stats
    stats_parser = tasks_subparsers.add_parser("stats", help="Show task statistics")
    
    # Cleanup
    cleanup_parser = tasks_subparsers.add_parser("cleanup", help="Clean up old tasks")
    cleanup_parser.add_argument("--days", type=int, default=30, help="Delete tasks older than N days")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    if args.command == "monitor":
        return monitor_main()
    elif args.command == "tasks":
        if not args.task_command:
            tasks_parser.print_help()
            return 1
        # Reconstruct args for task_cli
        task_args = [args.task_command]
        if hasattr(args, 'status') and args.status:
            task_args.extend(['--status', args.status])
        if hasattr(args, 'issue_key'):
            task_args.append(args.issue_key)
        if hasattr(args, 'comment_id'):
            task_args.append(args.comment_id)
        if hasattr(args, 'days'):
            task_args.extend(['--days', str(args.days)])
        
        # Override sys.argv for task_cli
        original_argv = sys.argv
        sys.argv = ['task_cli'] + task_args
        try:
            return task_cli_main()
        finally:
            sys.argv = original_argv
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
