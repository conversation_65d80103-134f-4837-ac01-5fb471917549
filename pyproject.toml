[project]
name = "agent-py"
version = "0.1.0"
description = "Jira-Integrated AI Task Automation"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiohttp>=3.12.15",
    "asyncio>=4.0.0",
    "mini-swe-agent>=1.9.1",
    "python-dotenv>=1.1.1",
    "requests>=2.32.5",
    "schedule>=1.2.2",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
]

[project.scripts]
agent-py-monitor = "agent_py.monitor:main"
agent-py-tasks = "agent_py.task_cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
