# Agent-Py: <PERSON><PERSON>集成AI任务自动化

一个基于Python的自动化系统，用于监控Jira评论中的AI任务标记，并使用AI代理执行这些任务。

## 功能特性

- **Jira集成**: 监控Jira评论中的AI任务标记
- **多仓库支持**: 可以克隆和处理不同的Git仓库
- **自动化工作流**: 从任务检测到结果发布的完整工作流
- **分支管理**: 自动创建和管理分支
- **AI代理执行**: 使用MonitoringAgent执行任务
- **结果报告**: 将执行结果发布回Jira评论
- **任务管理**: 防止重复处理相同的评论
- **全面日志记录**: 所有代理活动都记录到专用文件
- **HTTP日志服务器**: 内置HTTP服务器，通过URL提供日志文件
- **任务持久化**: 存储任务信息用于跟踪和审计

## 工作流程

对于在Jira评论中找到的每个AI任务，系统会：

1. **Git克隆**: 从 `ssh://******************:30004/gf/{project_name}.git` 克隆指定项目
2. **分支检出**: 创建并检出分支 `{version}_{issue_key}_{comment_id}`
3. **代理执行**: 在项目目录中运行MonitoringAgent
4. **提交更改**: 使用描述性消息提交所有更改
5. **推送更改**: 将分支推送到远程仓库
6. **清理**: 从文件系统删除本地仓库
7. **结果发布**: 将执行结果发布回原始Jira问题

## AI任务格式

要触发AI任务，请在Jira问题中添加以下格式的评论：

```
```ai-task
project: gifts/pb_package
description: 修复支付处理模块中的错误
为失败的交易添加适当的错误处理
确保覆盖所有边缘情况
```
```

### 必需字段

- **project**: Git仓库名称（例如：`gifts/pb_package`）
- **description**: 任务描述（可以是多行）

### 可选字段

- **version**: 基础分支版本（默认：`master`）

## 安装

### 前提条件

- Python 3.13+
- Git
- 访问Jira实例的权限
- 访问Git仓库的权限

### 使用uv安装

```bash
# 克隆仓库
git clone <repository-url>
cd agent-py

# 安装依赖
uv sync

# 安装开发依赖（包括测试工具）
uv sync --extra test
```

### 使用pip安装

```bash
# 克隆仓库
git clone <repository-url>
cd agent-py

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

## 配置

1. 复制环境变量模板：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件并配置您的设置：

### Jira配置

```bash
# Jira配置
JIRA_BASE_URL=https://your-jira-instance.com
JIRA_USERNAME=your-username
JIRA_PASSWORD=your-password

# 或者使用API令牌
JIRA_API_TOKEN=your-api-token

# 查询配置
JIRA_JQL=commentDate >= -1d
JIRA_SYNC_INTERVAL_MINUTES=5
```

### 工作空间配置

```bash
# 工作空间配置
WORKSPACE_DIR=./workspace

# 任务和日志存储
DATA_TASKS_DIR=data/tasks
DATA_LOGS_DIR=data/logs
```

### HTTP服务器配置

```bash
# HTTP服务器配置（用于提供日志文件）
LOG_SERVER_HOST=0.0.0.0
LOG_SERVER_PORT=8080
```

### AI模型配置

```bash
# AI模型配置
AI_MODEL_NAME=openrouter/z-ai/glm-4.5-air:free
```

您可以使用LiteLLM支持的任何模型，例如：
- `openrouter/z-ai/glm-4.5-air:free`（默认，免费模型）
- `gpt-4o-mini`
- `claude-3-haiku-20240307`
- `gemini/gemini-1.5-flash`

## 使用方法

### 运行应用程序

启动Jira评论监控器：

```bash
# 使用主入口点
python main.py monitor

# 使用简单入口点
python monitor.py

# 使用Makefile
make run-monitor
```

监控器将：
- 每5分钟检查一次新的Jira评论（可配置）
- 处理评论中找到的任何AI任务
- 将所有活动记录到控制台

### 运行测试

项目使用pytest进行测试。运行测试：

```bash
# 运行所有测试
pytest tests/

# 运行详细输出的测试
pytest tests/ -v

# 运行特定测试文件
pytest tests/test_parser.py -v

# 或使用Makefile
make test
make test-verbose
```

### 管理任务

使用任务CLI管理和监控任务：

```bash
# 列出所有任务
python tasks.py list

# 按状态列出任务
python tasks.py list --status completed

# 显示详细任务信息
python tasks.py show PROJ-123 12345

# 查看任务日志
python tasks.py logs PROJ-123 12345

# 显示任务统计
python tasks.py stats

# 清理旧任务（超过30天）
python tasks.py cleanup --days 30
```

## HTTP日志服务器

系统包含用于提供日志文件的内置HTTP服务器：

- 监控器开始时自动启动
- 在 `http://{HOST}:{PORT}/{log_filename}.log` 提供日志文件
- 默认配置：`0.0.0.0:8080`
- 安全访问：仅提供日志目录内的 `.log` 文件
- 日志URL在Jira评论中发布，便于访问

## 示例输出

### 1. 任务检测
```
[2024-01-01 10:00:00] 检查新的Jira评论...
[2024-01-01 10:00:01] 在问题PROJ-123中找到AI任务
[2024-01-01 10:00:01] 项目：gifts/pb_package
[2024-01-01 10:00:01] 描述：修复支付处理模块中的错误
```

### 2. 任务执行
```
[2024-01-01 10:00:02] 克隆仓库到./workspace/gifts_pb_package
[2024-01-01 10:00:05] 检出分支：3.11.1_PROJ-123_12345
[2024-01-01 10:00:06] 使用AI模型：openrouter/z-ai/glm-4.5-air:free
[2024-01-01 10:00:06] 为任务运行MonitoringAgent：修复支付处理模块中的错误
[2024-01-01 10:01:30] 代理完成，状态：success，消息：任务成功完成
[2024-01-01 10:01:31] 提交更改成功
[2024-01-01 10:01:32] 更改成功推送到远程仓库
[2024-01-01 10:01:33] 仓库目录删除成功
```

### 3. 结果评论
```
AI任务成功完成

**问题：** PROJ-123
**分支：** 3.11.1_PROJ-123_12345
**状态：** success
**消息：** 任务成功完成
**项目：** gifts/pb_package

任务已完成，更改已提交到分支 `3.11.1_PROJ-123_12345`。

**日志文件：** http://0.0.0.0:8080/task_abc123def456.log
```

## 项目结构

```
agent-py/
├── src/agent_py/           # 主要源代码
│   ├── __init__.py
│   ├── monitor.py          # 主监控器
│   ├── jira_client.py      # Jira API客户端
│   ├── jira_parser.py      # 评论解析器
│   ├── task_manager.py     # 任务管理
│   ├── git_operations.py   # Git操作
│   ├── http_server.py      # HTTP日志服务器
│   └── agents/
│       └── monitoring.py   # 监控代理
├── tests/                  # 测试文件
├── data/                   # 数据目录
│   ├── tasks/             # 任务存储
│   └── logs/              # 日志文件
├── workspace/             # 临时工作空间
├── main.py               # 主入口点
├── monitor.py            # 简单监控器入口点
├── tasks.py              # 任务管理CLI
├── demo_http_server.py   # HTTP服务器演示
├── .env.example          # 环境变量模板
├── requirements.txt      # Python依赖
├── pyproject.toml        # 项目配置
└── README.md             # 英文文档
```

## 错误处理

系统包含全面的错误处理：

- **Git错误**: 仓库克隆和分支操作
- **代理错误**: AI代理执行失败
- **Jira错误**: API通信问题
- **解析错误**: 无效评论格式处理

所有错误都会被记录并作为评论发布回原始Jira问题。

## 依赖项

- **Python 3.13+**
- **aiohttp**: 用于Jira API的异步HTTP客户端
- **mini-swe-agent**: AI代理框架
- **python-dotenv**: 环境变量管理
- **schedule**: 任务调度

## 测试

项目使用pytest进行全面测试：

### 测试覆盖

- **解析器测试**: AI任务评论解析
- **Jira客户端测试**: API交互（模拟）
- **Git操作测试**: 仓库操作
- **HTTP服务器测试**: 日志文件服务
- **任务管理测试**: 任务存储和检索
- **集成测试**: 端到端工作流

### 运行测试

```bash
# 运行所有测试
uv run pytest tests/

# 运行特定测试
uv run pytest tests/test_parser.py -v

# 运行覆盖率测试
uv run pytest tests/ --cov=src --cov-report=html
```

## 许可证

[在此添加许可证信息]

## 贡献

[在此添加贡献指南]

## 支持

如有问题或需要支持，请：
1. 检查现有的GitHub问题
2. 创建新问题并提供详细描述
3. 包含相关日志和错误消息
