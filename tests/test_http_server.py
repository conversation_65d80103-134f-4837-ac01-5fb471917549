"""
Tests for the HTTP log server functionality
"""

import os
import tempfile
import time
import requests
import pytest
from pathlib import Path
from src.agent_py.http_server import LogServer, get_log_server, start_log_server, stop_log_server
from src.agent_py.task_manager import TaskManager


class TestLogServer:
    """Test cases for LogServer class"""
    
    def setup_method(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.logs_dir = os.path.join(self.temp_dir, "logs")
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # Create a test log file
        self.test_log_content = "[2024-01-01T10:00:00] Test log message\n[2024-01-01T10:01:00] Another message\n"
        self.test_log_file = os.path.join(self.logs_dir, "task_test123.log")
        with open(self.test_log_file, 'w') as f:
            f.write(self.test_log_content)
    
    def teardown_method(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_server_start_stop(self):
        """Test starting and stopping the server"""
        server = LogServer(host="127.0.0.1", port=8081, logs_dir=self.logs_dir)
        
        # Test starting
        base_url = server.start()
        assert base_url == "http://127.0.0.1:8081"
        assert server.is_running()
        
        # Wait a moment for server to be ready
        time.sleep(0.2)
        
        # Test stopping
        server.stop()
        assert not server.is_running()
    
    def test_serve_log_file(self):
        """Test serving a log file via HTTP"""
        server = LogServer(host="127.0.0.1", port=8082, logs_dir=self.logs_dir)
        
        try:
            server.start()
            time.sleep(0.2)  # Wait for server to start
            
            # Request the log file
            response = requests.get("http://127.0.0.1:8082/task_test123.log", timeout=5)
            
            assert response.status_code == 200
            assert response.text == self.test_log_content
            assert response.headers['Content-Type'] == 'text/plain; charset=utf-8'
            
        finally:
            server.stop()
    
    def test_security_restrictions(self):
        """Test that only .log files can be accessed"""
        server = LogServer(host="127.0.0.1", port=8083, logs_dir=self.logs_dir)
        
        # Create a non-log file
        non_log_file = os.path.join(self.logs_dir, "secret.txt")
        with open(non_log_file, 'w') as f:
            f.write("Secret content")
        
        try:
            server.start()
            time.sleep(0.2)
            
            # Try to access non-log file
            response = requests.get("http://127.0.0.1:8083/secret.txt", timeout=5)
            assert response.status_code == 404
            
            # Try to access file outside logs directory
            response = requests.get("http://127.0.0.1:8083/../../../etc/passwd", timeout=5)
            assert response.status_code == 404
            
        finally:
            server.stop()
    
    def test_nonexistent_file(self):
        """Test accessing a non-existent log file"""
        server = LogServer(host="127.0.0.1", port=8084, logs_dir=self.logs_dir)
        
        try:
            server.start()
            time.sleep(0.2)
            
            response = requests.get("http://127.0.0.1:8084/nonexistent.log", timeout=5)
            assert response.status_code == 404
            
        finally:
            server.stop()
    
    def test_get_log_url(self):
        """Test getting log URL for a specific task"""
        server = LogServer(host="127.0.0.1", port=8085, logs_dir=self.logs_dir)
        
        # Create task manager with same logs directory
        tm = TaskManager(logs_dir=self.logs_dir)
        
        # Get log URL
        url = server.get_log_url("TEST-123", "456")
        
        # URL should contain the correct format
        assert url.startswith("http://127.0.0.1:8085/")
        assert url.endswith(".log")


class TestGlobalServerFunctions:
    """Test global server management functions"""
    
    def setup_method(self):
        """Set up test environment"""
        # Clean up any existing global server
        stop_log_server()
    
    def teardown_method(self):
        """Clean up test environment"""
        stop_log_server()
    
    def test_global_server_management(self):
        """Test global server start/stop functions"""
        # Set environment variables for testing
        os.environ["LOG_SERVER_HOST"] = "127.0.0.1"
        os.environ["LOG_SERVER_PORT"] = "8086"
        
        try:
            # Start global server
            base_url = start_log_server()
            assert base_url == "http://127.0.0.1:8086"
            
            server = get_log_server()
            assert server.is_running()
            
            # Stop global server
            stop_log_server()
            
            # Server should be cleaned up
            server = get_log_server()
            assert not server.is_running()
            
        finally:
            # Clean up environment variables
            os.environ.pop("LOG_SERVER_HOST", None)
            os.environ.pop("LOG_SERVER_PORT", None)


if __name__ == "__main__":
    pytest.main([__file__])
