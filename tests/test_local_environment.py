"""
Tests for LocalEnvironment with cwd parameter
"""

import pytest
import tempfile
import os
from minisweagent.environments.local import LocalEnvironment


class TestLocalEnvironment:
    """Test cases for LocalEnvironment functionality"""

    def test_local_environment_with_cwd(self):
        """Test that LocalEnvironment respects the cwd parameter"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file in the temporary directory
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, "w") as f:
                f.write("Hello from test directory!")
            
            # Test LocalEnvironment with cwd
            env = LocalEnvironment(cwd=temp_dir)
            
            # Test 1: Check current directory
            result = env.execute("pwd")
            assert temp_dir in result["output"]
            assert result["returncode"] == 0
            
            # Test 2: List files in directory
            result = env.execute("ls -la")
            assert "test.txt" in result["output"]
            assert result["returncode"] == 0
            
            # Test 3: Read the test file
            result = env.execute("cat test.txt")
            assert "Hello from test directory!" in result["output"]
            assert result["returncode"] == 0
            
            # Test 4: Create a new file
            result = env.execute("echo 'Created by agent' > agent_test.txt")
            assert result["returncode"] == 0
            
            # Verify the file was created
            result = env.execute("cat agent_test.txt")
            assert "Created by agent" in result["output"]

    def test_local_environment_without_cwd(self):
        """Test LocalEnvironment without cwd parameter (should use current directory)"""
        env = LocalEnvironment()
        
        # Should use current working directory
        result = env.execute("pwd")
        current_dir = os.getcwd()
        
        # Should be in the current directory
        assert current_dir in result["output"]

    def test_local_environment_command_failure(self):
        """Test LocalEnvironment with a failing command"""
        env = LocalEnvironment()
        
        # Run a command that should fail
        result = env.execute("ls /nonexistent/directory")
        
        # Should have non-zero return code
        assert result["returncode"] != 0
        assert "No such file or directory" in result["output"] or "cannot access" in result["output"]

    def test_local_environment_with_env_vars(self):
        """Test LocalEnvironment with custom environment variables"""
        env = LocalEnvironment(env={"TEST_VAR": "test_value"})
        
        # Test that custom environment variable is available
        result = env.execute("echo $TEST_VAR")
        assert "test_value" in result["output"]
        assert result["returncode"] == 0

    def test_local_environment_timeout(self):
        """Test LocalEnvironment with timeout"""
        # Use a very short timeout
        env = LocalEnvironment(timeout=1)
        
        # This command should timeout (sleep for 2 seconds with 1 second timeout)
        with pytest.raises(Exception):  # Should raise TimeoutExpired
            env.execute("sleep 2")

    def test_local_environment_multiline_output(self):
        """Test LocalEnvironment with commands that produce multiline output"""
        with tempfile.TemporaryDirectory() as temp_dir:
            env = LocalEnvironment(cwd=temp_dir)
            
            # Create multiple files
            env.execute("touch file1.txt file2.txt file3.txt")
            
            # List files (should produce multiline output)
            result = env.execute("ls -1")
            
            assert result["returncode"] == 0
            assert "file1.txt" in result["output"]
            assert "file2.txt" in result["output"]
            assert "file3.txt" in result["output"]

    def test_local_environment_special_characters(self):
        """Test LocalEnvironment with special characters in commands"""
        with tempfile.TemporaryDirectory() as temp_dir:
            env = LocalEnvironment(cwd=temp_dir)
            
            # Test with special characters
            result = env.execute("echo 'Hello & World! $USER @home'")
            
            assert result["returncode"] == 0
            assert "Hello & World!" in result["output"]

    def test_local_environment_working_directory_isolation(self):
        """Test that different LocalEnvironment instances don't interfere"""
        with tempfile.TemporaryDirectory() as temp_dir1:
            with tempfile.TemporaryDirectory() as temp_dir2:
                env1 = LocalEnvironment(cwd=temp_dir1)
                env2 = LocalEnvironment(cwd=temp_dir2)
                
                # Create different files in each environment
                env1.execute("echo 'env1' > test.txt")
                env2.execute("echo 'env2' > test.txt")
                
                # Read files from each environment
                result1 = env1.execute("cat test.txt")
                result2 = env2.execute("cat test.txt")
                
                assert "env1" in result1["output"]
                assert "env2" in result2["output"]
                assert result1["output"] != result2["output"]
