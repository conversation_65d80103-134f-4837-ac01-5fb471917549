"""
Tests for the TaskManager
"""

import pytest
import tempfile
import json
from pathlib import Path
from src.agent_py.task_manager import TaskManager


class TestTaskManager:
    """Test cases for TaskManager functionality"""

    @pytest.fixture
    def temp_dirs(self):
        """Create temporary directories for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            tasks_dir = f"{temp_dir}/tasks"
            logs_dir = f"{temp_dir}/logs"
            yield tasks_dir, logs_dir

    @pytest.fixture
    def task_manager(self, temp_dirs):
        """Create TaskManager instance with temporary directories"""
        tasks_dir, logs_dir = temp_dirs
        return TaskManager(tasks_dir, logs_dir)

    @pytest.fixture
    def sample_task(self):
        """Sample task data"""
        return {
            "issue_key": "TEST-123",
            "comment_id": "12345",
            "project_name": "test/project",
            "version": "1.0.0",
            "task_description": "Test task description",
            "branch": "1.0.0_TEST-123_12345"
        }

    def test_initial_state(self, task_manager, sample_task):
        """Test that task is not processed initially"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        assert not task_manager.is_task_processed(issue_key, comment_id)

    def test_save_and_retrieve_task(self, task_manager, sample_task):
        """Test saving and retrieving task info"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        # Save task
        task_manager.save_task_info(sample_task, "created")
        
        # Check if processed
        assert task_manager.is_task_processed(issue_key, comment_id)
        
        # Retrieve task
        retrieved_task = task_manager.get_task_info(issue_key, comment_id)
        assert retrieved_task is not None
        assert retrieved_task["task"]["issue_key"] == issue_key
        assert retrieved_task["status"] == "created"

    def test_update_task_status(self, task_manager, sample_task):
        """Test updating task status"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        # Save initial task
        task_manager.save_task_info(sample_task, "created")
        
        # Update status
        task_manager.update_task_status(issue_key, comment_id, "processing")
        
        # Verify update
        updated_task = task_manager.get_task_info(issue_key, comment_id)
        assert updated_task["status"] == "processing"

    def test_task_logs(self, task_manager, sample_task):
        """Test writing and reading task logs"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        # Write logs
        task_manager.write_log(issue_key, comment_id, "Test log message 1")
        task_manager.write_log(issue_key, comment_id, "Test log message 2")
        
        # Read logs
        logs = task_manager.get_task_logs(issue_key, comment_id)
        assert "Test log message 1" in logs
        assert "Test log message 2" in logs

    def test_list_tasks(self, task_manager, sample_task):
        """Test listing tasks with status filtering"""
        # Save task
        task_manager.save_task_info(sample_task, "processing")
        
        # List all tasks
        all_tasks = task_manager.list_tasks()
        assert len(all_tasks) == 1
        
        # List by status
        processing_tasks = task_manager.list_tasks("processing")
        assert len(processing_tasks) == 1
        
        completed_tasks = task_manager.list_tasks("completed")
        assert len(completed_tasks) == 0

    def test_complete_task_with_result(self, task_manager, sample_task):
        """Test completing task with result"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        # Save and process task
        task_manager.save_task_info(sample_task, "created")
        task_manager.update_task_status(issue_key, comment_id, "processing")
        
        # Complete with result
        result = {
            "success": True,
            "branch": "1.0.0_TEST-123_12345",
            "status": "success",
            "message": "Task completed successfully"
        }
        task_manager.update_task_status(issue_key, comment_id, "completed", result)
        
        # Verify completion
        final_task = task_manager.get_task_info(issue_key, comment_id)
        assert final_task["status"] == "completed"
        assert final_task["result"]["success"] is True

    def test_different_comments_different_tasks(self, task_manager):
        """Test that different comments create different tasks"""
        issue_key = "TEST-456"
        comment_id_1 = "11111"
        comment_id_2 = "22222"
        
        task_1 = {
            "issue_key": issue_key,
            "comment_id": comment_id_1,
            "project_name": "test/project1",
            "task_description": "First task"
        }
        
        task_2 = {
            "issue_key": issue_key,
            "comment_id": comment_id_2,
            "project_name": "test/project2",
            "task_description": "Second task"
        }
        
        # Save both tasks
        task_manager.save_task_info(task_1, "created")
        task_manager.save_task_info(task_2, "created")
        
        # Both should be processed
        assert task_manager.is_task_processed(issue_key, comment_id_1)
        assert task_manager.is_task_processed(issue_key, comment_id_2)
        
        # Should have 2 tasks total
        all_tasks = task_manager.list_tasks()
        assert len(all_tasks) == 2

    def test_get_log_file_path(self, task_manager, sample_task):
        """Test getting log file path"""
        issue_key = sample_task["issue_key"]
        comment_id = sample_task["comment_id"]
        
        log_path = task_manager.get_log_file_path(issue_key, comment_id)
        assert log_path.endswith(".log")
        assert "task_" in log_path

    def test_nonexistent_task(self, task_manager):
        """Test retrieving nonexistent task"""
        result = task_manager.get_task_info("NONEXISTENT", "99999")
        assert result is None
        
        logs = task_manager.get_task_logs("NONEXISTENT", "99999")
        assert logs == ""

    def test_update_nonexistent_task(self, task_manager):
        """Test updating nonexistent task (should not crash)"""
        # This should not raise an exception
        task_manager.update_task_status("NONEXISTENT", "99999", "failed")

    def test_cleanup_old_tasks(self, task_manager, sample_task):
        """Test cleanup functionality (basic test)"""
        # Save a task
        task_manager.save_task_info(sample_task, "completed")

        # Cleanup with 1000 days (should not delete recent tasks)
        cleaned_count = task_manager.cleanup_old_tasks(days=1000)

        # Should still exist (task is recent, not 1000 days old)
        assert task_manager.is_task_processed(sample_task["issue_key"], sample_task["comment_id"])
        assert cleaned_count == 0
