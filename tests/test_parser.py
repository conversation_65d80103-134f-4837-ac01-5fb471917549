"""
Tests for the Jira comment parser
"""

import pytest
from src.agent_py.jira_parser import parse_comment


class TestJiraParser:
    """Test cases for Jira comment parsing"""

    @pytest.fixture
    def sample_comment(self):
        """Sample Jira comment with AI task"""
        return {
            "id": "12345",
            "body": """
This is a regular comment.

```ai-task
project: gifts/pb_package
description: Fix the bug in the payment processing module
Add proper error handling for failed transactions
Ensure all edge cases are covered
```

Some more text after the task.
            """
        }

    @pytest.fixture
    def sample_issue(self):
        """Sample Jira issue"""
        return {
            "key": "PROJ-123",
            "fields": {
                "fixVersions": [
                    {"name": "3.11.1"}
                ]
            }
        }

    def test_parse_ai_task_comment(self, sample_comment, sample_issue):
        """Test parsing a valid AI task comment"""
        result = parse_comment(sample_comment, sample_issue)
        
        assert result["is_ai_task"] is True
        assert result["task_info"] is not None
        
        task = result["task_info"]
        assert task["issue_key"] == "PROJ-123"
        assert task["version"] == "3.11.1"
        assert task["project_name"] == "gifts/pb_package"
        assert task["comment_id"] == "12345"
        assert task["branch"] == "3.11.1_PROJ-123_12345"
        assert "Fix the bug in the payment processing module" in task["task_description"]
        assert "Add proper error handling for failed transactions" in task["task_description"]
        assert "Ensure all edge cases are covered" in task["task_description"]

    def test_parse_regular_comment(self, sample_issue):
        """Test parsing a regular comment without AI task"""
        regular_comment = {
            "id": "67890",
            "body": "This is just a regular comment without any AI task."
        }
        
        result = parse_comment(regular_comment, sample_issue)
        
        assert result["is_ai_task"] is False
        assert result["task_info"] is None
        assert result["original_comment"] == regular_comment

    def test_parse_incomplete_ai_task(self, sample_issue):
        """Test parsing an incomplete AI task (missing closing backticks)"""
        incomplete_comment = {
            "id": "11111",
            "body": """
```ai-task
project: test/project
description: This task is incomplete
            """
        }
        
        result = parse_comment(incomplete_comment, sample_issue)
        
        assert result["is_ai_task"] is False
        assert result["task_info"] is None

    def test_parse_ai_task_with_minimal_info(self, sample_issue):
        """Test parsing AI task with minimal required information"""
        minimal_comment = {
            "id": "22222",
            "body": """
```ai-task
project: minimal/project
description: Minimal task description
```
            """
        }
        
        result = parse_comment(minimal_comment, sample_issue)
        
        assert result["is_ai_task"] is True
        task = result["task_info"]
        assert task["project_name"] == "minimal/project"
        assert task["task_description"] == "Minimal task description"

    def test_parse_ai_task_without_project(self, sample_issue):
        """Test parsing AI task without project field"""
        no_project_comment = {
            "id": "33333",
            "body": """
```ai-task
description: Task without project
```
            """
        }
        
        result = parse_comment(no_project_comment, sample_issue)
        
        assert result["is_ai_task"] is True
        task = result["task_info"]
        assert task["project_name"] == ""  # Should be empty string
        assert task["task_description"] == "Task without project"

    def test_parse_ai_task_multiline_description(self, sample_issue):
        """Test parsing AI task with multiline description"""
        multiline_comment = {
            "id": "44444",
            "body": """
```ai-task
project: test/multiline
description: This is a multiline description
It spans multiple lines
And includes various details
About the task to be performed
```
            """
        }
        
        result = parse_comment(multiline_comment, sample_issue)
        
        assert result["is_ai_task"] is True
        task = result["task_info"]
        expected_description = """This is a multiline description
It spans multiple lines
And includes various details
About the task to be performed"""
        assert task["task_description"] == expected_description

    def test_parse_ai_task_with_fallback_version(self):
        """Test parsing AI task when issue has no fixVersions"""
        issue_no_version = {
            "key": "NO-VER-123",
            "fields": {
                "fixVersions": []
            }
        }
        
        comment = {
            "id": "55555",
            "body": """
```ai-task
project: test/no-version
description: Task with no version
```
            """
        }
        
        result = parse_comment(comment, issue_no_version)
        
        assert result["is_ai_task"] is True
        task = result["task_info"]
        assert task["version"] == "3.11.1"  # Fallback version
        assert task["branch"] == "3.11.1_NO-VER-123_55555"
