"""
Tests for backward compatibility - ensure old comments with 'tool:' field still work
"""

import pytest
from src.agent_py.jira_parser import parse_comment


class TestBackwardCompatibility:
    """Test cases for backward compatibility with old comment formats"""

    @pytest.fixture
    def sample_issue(self):
        """Sample Jira issue"""
        return {
            "key": "LEGACY-789",
            "fields": {
                "fixVersions": [{"name": "1.5.0"}]
            }
        }

    def test_old_comment_with_tool_field(self, sample_issue):
        """Test that old comments with tool field still work"""
        old_comment = {
            "id": "54321",
            "body": """
```ai-task
project: gifts/legacy_system
tool: aider
description: This is an old comment format
It should still work correctly
The tool field should be ignored
```
            """
        }
        
        result = parse_comment(old_comment, sample_issue)
        
        assert result["is_ai_task"] is True
        task = result["task_info"]
        
        assert task["issue_key"] == "LEGACY-789"
        assert task["comment_id"] == "54321"
        assert task["project_name"] == "gifts/legacy_system"
        
        # Verify the tool line is not included in description
        assert "tool: aider" not in task["task_description"]
        assert "This is an old comment format" in task["task_description"]
        assert "It should still work correctly" in task["task_description"]
        assert "The tool field should be ignored" in task["task_description"]
        
        # Verify ai_tool field is not present
        assert "ai_tool" not in task

    def test_mixed_content_with_tool_field(self, sample_issue):
        """Test comment with tool field mixed in description"""
        mixed_comment = {
            "id": "99999",
            "body": """
```ai-task
project: gifts/mixed_test
description: Fix the authentication system
tool: some-old-tool
Add proper session management
Implement OAuth2 integration
```
            """
        }
        
        result = parse_comment(mixed_comment, sample_issue)
        task = result["task_info"]
        
        # Should not include the tool line
        assert "tool: some-old-tool" not in task["task_description"]
        
        # Should include the actual description content
        expected_lines = [
            "Fix the authentication system",
            "Add proper session management",
            "Implement OAuth2 integration"
        ]
        
        for line in expected_lines:
            assert line in task["task_description"]

    def test_tool_field_at_beginning(self, sample_issue):
        """Test tool field at the beginning of the task block"""
        comment = {
            "id": "11111",
            "body": """
```ai-task
tool: legacy-tool
project: gifts/tool_first
description: Task with tool field first
This should still work
```
            """
        }
        
        result = parse_comment(comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/tool_first"
        assert "tool: legacy-tool" not in task["task_description"]
        assert "Task with tool field first" in task["task_description"]
        assert "This should still work" in task["task_description"]

    def test_tool_field_at_end(self, sample_issue):
        """Test tool field at the end of the task block"""
        comment = {
            "id": "22222",
            "body": """
```ai-task
project: gifts/tool_last
description: Task with tool field last
This should work too
tool: another-legacy-tool
```
            """
        }
        
        result = parse_comment(comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/tool_last"
        assert "tool: another-legacy-tool" not in task["task_description"]
        assert "Task with tool field last" in task["task_description"]
        assert "This should work too" in task["task_description"]

    def test_multiple_tool_fields(self, sample_issue):
        """Test comment with multiple tool fields (edge case)"""
        comment = {
            "id": "33333",
            "body": """
```ai-task
tool: first-tool
project: gifts/multiple_tools
tool: second-tool
description: Task with multiple tool fields
All tool fields should be ignored
tool: third-tool
```
            """
        }
        
        result = parse_comment(comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/multiple_tools"
        
        # None of the tool fields should be in description
        assert "tool: first-tool" not in task["task_description"]
        assert "tool: second-tool" not in task["task_description"]
        assert "tool: third-tool" not in task["task_description"]
        
        # Description content should be preserved
        assert "Task with multiple tool fields" in task["task_description"]
        assert "All tool fields should be ignored" in task["task_description"]

    def test_tool_field_with_special_characters(self, sample_issue):
        """Test tool field with special characters"""
        comment = {
            "id": "44444",
            "body": """
```ai-task
project: gifts/special_chars
tool: some-tool-with-dashes_and_underscores.v1.0
description: Task with special tool name
The tool field has special characters
```
            """
        }
        
        result = parse_comment(comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/special_chars"
        assert "tool: some-tool-with-dashes_and_underscores.v1.0" not in task["task_description"]
        assert "Task with special tool name" in task["task_description"]

    def test_tool_field_with_spaces_in_value(self, sample_issue):
        """Test tool field with spaces in the value"""
        comment = {
            "id": "55555",
            "body": """
```ai-task
project: gifts/tool_spaces
tool: tool with spaces
description: Task with spaced tool name
This should work fine
```
            """
        }
        
        result = parse_comment(comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/tool_spaces"
        assert "tool: tool with spaces" not in task["task_description"]
        assert "Task with spaced tool name" in task["task_description"]

    def test_new_format_without_tool_field(self, sample_issue):
        """Test that new format without tool field works correctly"""
        new_comment = {
            "id": "66666",
            "body": """
```ai-task
project: gifts/new_format
description: New format without tool field
This is the current expected format
```
            """
        }
        
        result = parse_comment(new_comment, sample_issue)
        task = result["task_info"]
        
        assert task["project_name"] == "gifts/new_format"
        assert "New format without tool field" in task["task_description"]
        assert "This is the current expected format" in task["task_description"]
        
        # Should not have any tool-related content
        assert "tool:" not in task["task_description"].lower()

    def test_case_insensitive_tool_field(self, sample_issue):
        """Test that tool field detection is case sensitive (should only match 'tool:')"""
        comment = {
            "id": "77777",
            "body": """
```ai-task
project: gifts/case_test
description: Test case sensitivity
Tool: uppercase-tool
TOOL: caps-tool
Tool field variations should not be filtered
TOOL field variations should not be filtered
```
            """
        }

        result = parse_comment(comment, sample_issue)
        task = result["task_info"]

        # Only lowercase 'tool:' should be filtered, others should remain
        assert "Tool: uppercase-tool" in task["task_description"]
        assert "TOOL: caps-tool" in task["task_description"]
        assert "Tool field variations should not be filtered" in task["task_description"]
        assert "TOOL field variations should not be filtered" in task["task_description"]
