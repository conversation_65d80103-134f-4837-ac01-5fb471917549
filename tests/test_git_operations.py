"""
Tests for git operations
"""

import pytest
import tempfile
import os
import subprocess
from src.agent_py.git_operations import clone_repository, setup_for_ai_task, commit_changes


class TestGitOperations:
    """Test cases for git operations"""

    def create_test_git_repo(self, repo_path):
        """Create a test git repository"""
        os.makedirs(repo_path, exist_ok=True)
        subprocess.run(["git", "init"], cwd=repo_path, check=True, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=repo_path, check=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=repo_path, check=True)

        # Create initial commit
        with open(os.path.join(repo_path, "README.md"), "w") as f:
            f.write("# Test Repository\n")
        subprocess.run(["git", "add", "README.md"], cwd=repo_path, check=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=repo_path, check=True)

    @pytest.mark.asyncio
    async def test_clone_repository_mock(self):
        """Test repository cloning functionality (mocked)"""
        # This test would require actual git operations
        # For now, we'll skip it to avoid network dependencies
        pytest.skip("Skipping git clone test to avoid network dependencies")

    @pytest.mark.asyncio
    async def test_setup_for_ai_task(self):
        """Test branch setup for AI task"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test git repository
            repo_path = os.path.join(temp_dir, "test_repo")
            self.create_test_git_repo(repo_path)

            # Setup branch for AI task
            branch_name = await setup_for_ai_task(
                "3.11.1",
                "TEST-123",
                "12345",
                repo_path
            )

            # Verify branch was created
            assert branch_name == "3.11.1_TEST-123_12345"

    @pytest.mark.asyncio
    async def test_commit_changes(self):
        """Test committing changes"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test repository and setup branch
            repo_path = os.path.join(temp_dir, "test_repo")
            self.create_test_git_repo(repo_path)

            await setup_for_ai_task("3.11.1", "TEST-123", "12345", repo_path)

            # Create a test file
            test_file = os.path.join(repo_path, "test_file.txt")
            with open(test_file, "w") as f:
                f.write("This is a test file created by the AI agent\n")

            # Commit changes
            await commit_changes(repo_path, "Test commit from AI agent")

            # Verify commit was successful (no exception means success)
            assert os.path.exists(test_file)

    @pytest.mark.asyncio
    async def test_commit_no_changes(self):
        """Test committing when there are no changes"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test repository and setup branch
            repo_path = os.path.join(temp_dir, "test_repo")
            self.create_test_git_repo(repo_path)

            await setup_for_ai_task("3.11.1", "TEST-123", "12345", repo_path)

            # Try to commit without any changes - should raise exception
            with pytest.raises(Exception):
                await commit_changes(repo_path, "Empty commit")

    @pytest.mark.asyncio
    async def test_setup_task_creates_unique_branch(self):
        """Test that different tasks create different branches"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create first repository
            repo_path1 = os.path.join(temp_dir, "test_repo1")
            self.create_test_git_repo(repo_path1)
            branch1 = await setup_for_ai_task("1.0.0", "TASK-1", "111", repo_path1)

            # Create second repository
            repo_path2 = os.path.join(temp_dir, "test_repo2")
            self.create_test_git_repo(repo_path2)
            branch2 = await setup_for_ai_task("1.0.0", "TASK-2", "222", repo_path2)

            # Branches should be different
            assert branch1 != branch2
            assert branch1 == "1.0.0_TASK-1_111"
            assert branch2 == "1.0.0_TASK-2_222"

    def test_git_operations_functions_exist(self):
        """Test that git operation functions are importable"""
        # This is a basic test to ensure the functions exist
        from src.agent_py.git_operations import clone_repository, setup_for_ai_task, commit_changes

        assert callable(clone_repository)
        assert callable(setup_for_ai_task)
        assert callable(commit_changes)
