"""
Tests for the full workflow (without actual Jira/Git operations)
"""

import pytest
import tempfile
from src.agent_py.jira_parser import parse_comment
from src.agent_py.task_manager import TaskManager


class TestWorkflow:
    """Test cases for the complete workflow"""

    @pytest.fixture
    def sample_comment(self):
        """Sample Jira comment with AI task"""
        return {
            "id": "67890",
            "body": """
This is a comment about a bug.

```ai-task
project: gifts/pb_package
description: Fix the payment processing bug
Add proper validation for credit card numbers
Ensure PCI compliance
```

Please review this task.
            """
        }

    @pytest.fixture
    def sample_issue(self):
        """Sample Jira issue"""
        return {
            "key": "GIFT-456",
            "fields": {
                "fixVersions": [
                    {"name": "2.5.0"}
                ]
            }
        }

    @pytest.fixture
    def task_manager(self):
        """Create TaskManager with temporary directories"""
        with tempfile.TemporaryDirectory() as temp_dir:
            tasks_dir = f"{temp_dir}/tasks"
            logs_dir = f"{temp_dir}/logs"
            yield TaskManager(tasks_dir, logs_dir)

    def test_full_workflow_simulation(self, sample_comment, sample_issue, task_manager):
        """Test the complete workflow with task management"""
        # Step 1: Parse comment
        parsed = parse_comment(sample_comment, sample_issue)
        
        assert parsed["is_ai_task"] is True
        task = parsed["task_info"]
        
        assert task["issue_key"] == "GIFT-456"
        assert task["comment_id"] == "67890"
        assert task["project_name"] == "gifts/pb_package"
        assert task["branch"] == "2.5.0_GIFT-456_67890"
        
        issue_key = task["issue_key"]
        comment_id = task["comment_id"]
        
        # Step 2: Check if task already processed
        assert not task_manager.is_task_processed(issue_key, comment_id)
        
        # Step 3: Save task info
        task_manager.save_task_info(task, "created")
        assert task_manager.is_task_processed(issue_key, comment_id)
        
        # Step 4: Simulate task processing
        task_manager.update_task_status(issue_key, comment_id, "processing")
        task_manager.write_log(issue_key, comment_id, "Starting task processing")
        task_manager.write_log(issue_key, comment_id, f"Cloning repository: ssh://******************:30004/gf/{task['project_name']}.git")
        task_manager.write_log(issue_key, comment_id, f"Creating branch: {task['branch']}")
        task_manager.write_log(issue_key, comment_id, "Running MonitoringAgent...")
        task_manager.write_log(issue_key, comment_id, "Agent execution completed successfully")
        task_manager.write_log(issue_key, comment_id, "Committing changes...")
        task_manager.write_log(issue_key, comment_id, "Changes committed successfully")
        
        # Step 5: Complete task
        result = {
            "success": True,
            "branch": task["branch"],
            "status": "success",
            "message": "Payment processing bug fixed successfully"
        }
        task_manager.update_task_status(issue_key, comment_id, "completed", result)
        
        # Step 6: Verify task state
        final_task = task_manager.get_task_info(issue_key, comment_id)
        assert final_task["status"] == "completed"
        assert final_task["result"]["success"] is True
        
        # Step 7: Check logs
        logs = task_manager.get_task_logs(issue_key, comment_id)
        assert "Starting task processing" in logs
        assert "Changes committed successfully" in logs
        
        # Step 8: Test duplicate prevention
        assert task_manager.is_task_processed(issue_key, comment_id)

    def test_multiple_comments_workflow(self, sample_issue):
        """Test processing multiple different comments"""
        comments = [
            {
                "id": "11111",
                "body": """
```ai-task
project: gifts/payment
description: Fix payment gateway integration
```
                """
            },
            {
                "id": "22222",
                "body": """
```ai-task
project: gifts/inventory
description: Optimize inventory management
```
                """
            }
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            tasks_dir = f"{temp_dir}/tasks"
            logs_dir = f"{temp_dir}/logs"
            tm = TaskManager(tasks_dir, logs_dir)
            
            processed_tasks = []
            
            for comment in comments:
                parsed = parse_comment(comment, sample_issue)
                if parsed["is_ai_task"]:
                    task = parsed["task_info"]
                    issue_key = task["issue_key"]
                    comment_id = task["comment_id"]
                    
                    if not tm.is_task_processed(issue_key, comment_id):
                        tm.save_task_info(task, "created")
                        tm.update_task_status(issue_key, comment_id, "completed", {"success": True})
                        processed_tasks.append(task)
            
            assert len(processed_tasks) == 2
            
            all_tasks = tm.list_tasks()
            assert len(all_tasks) == 2

    def test_workflow_with_failed_task(self, sample_comment, sample_issue, task_manager):
        """Test workflow when task fails"""
        # Parse and save task
        parsed = parse_comment(sample_comment, sample_issue)
        task = parsed["task_info"]
        issue_key = task["issue_key"]
        comment_id = task["comment_id"]
        
        task_manager.save_task_info(task, "created")
        task_manager.update_task_status(issue_key, comment_id, "processing")
        
        # Simulate failure
        task_manager.write_log(issue_key, comment_id, "Starting task processing")
        task_manager.write_log(issue_key, comment_id, "Error: Repository clone failed")
        
        # Mark as failed
        error_result = {
            "success": False,
            "error": "Repository clone failed"
        }
        task_manager.update_task_status(issue_key, comment_id, "failed", error_result)
        
        # Verify failure state
        final_task = task_manager.get_task_info(issue_key, comment_id)
        assert final_task["status"] == "failed"
        assert final_task["result"]["success"] is False
        
        # Check error logs
        logs = task_manager.get_task_logs(issue_key, comment_id)
        assert "Repository clone failed" in logs

    def test_workflow_task_statistics(self, sample_issue):
        """Test workflow with task statistics"""
        with tempfile.TemporaryDirectory() as temp_dir:
            tasks_dir = f"{temp_dir}/tasks"
            logs_dir = f"{temp_dir}/logs"
            tm = TaskManager(tasks_dir, logs_dir)
            
            # Create tasks with different statuses
            tasks_data = [
                ("STAT-1", "111", "created"),
                ("STAT-2", "222", "processing"),
                ("STAT-3", "333", "completed"),
                ("STAT-4", "444", "failed"),
                ("STAT-5", "555", "completed"),
            ]
            
            for issue_key, comment_id, status in tasks_data:
                task = {
                    "issue_key": issue_key,
                    "comment_id": comment_id,
                    "project_name": "test/project",
                    "task_description": f"Task for {issue_key}"
                }
                tm.save_task_info(task, status)
            
            # Check statistics
            all_tasks = tm.list_tasks()
            created_tasks = tm.list_tasks("created")
            processing_tasks = tm.list_tasks("processing")
            completed_tasks = tm.list_tasks("completed")
            failed_tasks = tm.list_tasks("failed")
            
            assert len(all_tasks) == 5
            assert len(created_tasks) == 1
            assert len(processing_tasks) == 1
            assert len(completed_tasks) == 2
            assert len(failed_tasks) == 1

    def test_workflow_duplicate_prevention(self, sample_comment, sample_issue, task_manager):
        """Test that duplicate comments are properly prevented"""
        # Process the same comment twice
        parsed = parse_comment(sample_comment, sample_issue)
        task = parsed["task_info"]
        issue_key = task["issue_key"]
        comment_id = task["comment_id"]
        
        # First processing
        assert not task_manager.is_task_processed(issue_key, comment_id)
        task_manager.save_task_info(task, "created")
        task_manager.update_task_status(issue_key, comment_id, "completed", {"success": True})
        
        # Second attempt (should be detected as duplicate)
        assert task_manager.is_task_processed(issue_key, comment_id)
        
        # Should still have only one task
        all_tasks = task_manager.list_tasks()
        assert len(all_tasks) == 1
