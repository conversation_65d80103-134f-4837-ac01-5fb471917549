# Jira Configuration
JIRA_BASE_URL=https://your-jira-instance.com
JIRA_USERNAME=your-username
JIRA_PASSWORD=your-password
# Alternative: Use authorization token
# JIRA_AUTHORIZATION=your-base64-encoded-credentials
# Alternative: Use API token
# JIRA_API_TOKEN=your-api-token
# Alternative: Use cookie-based auth
# JIRA_COOKIE=JSESSIONID=your-session-id
# JIRA_HOME=https://your-jira-instance.com

# Jira Query Configuration
JIRA_JQL=commentDate >= -1d
JIRA_SYNC_INTERVAL_MINUTES=5

# Workspace Configuration
WORKSPACE_DIR=./workspace

# Task and Log Storage
DATA_TASKS_DIR=data/tasks
DATA_LOGS_DIR=data/logs

# HTTP Server Configuration (for serving log files)
LOG_SERVER_HOST=0.0.0.0
LOG_SERVER_PORT=8080

# Repository Configuration (for legacy single-repo mode)
REPO_PATH=.
